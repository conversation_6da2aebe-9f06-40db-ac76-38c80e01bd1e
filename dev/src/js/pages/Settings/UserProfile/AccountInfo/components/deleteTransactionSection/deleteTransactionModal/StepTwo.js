import React from 'react';
import { Paper, Heading, Text, InputCheckbox, Separator, Flex, Box } from '@majoo-ui/react';
import { useFormContext, Controller } from 'react-hook-form';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';

const StepTwo = () => {
    const { t } = useAccountInfo();
    const {
        watch,
        control,
        formState: { errors },
    } = useFormContext();

    const startDate = watch('startDate');
    const endDate = watch('endDate');

    const CheckboxLabel = ({ children }) => (
        <Text color="secondary" css={{ flex: 1 }}>
            {children}
        </Text>
    );

    return (
        <Paper responsive css={{ display: 'flex', flexDirection: 'column' }}>
            <Heading heading="sectionTitle">{t('deleteTransaction.termsTitle')}</Heading>
            <br />
            <Text color="secondary">{t('deleteTransaction.deleteInfo')}</Text>
            <ol style={{ marginTop: 0, paddingLeft: '1.5rem' }}>
                <li>
                    <Text color="secondary">{t('deleteTransaction.deleteInfo1')}</Text>
                </li>
                <li>
                    <Text color="secondary">
                        {t('deleteTransaction.deleteInfo2')} <i>{t('deleteTransaction.italicDeleteInfo')}</i>
                    </Text>
                </li>
                <li>
                    <Text color="secondary">
                        {t('deleteTransaction.deleteInfo3')} <i>{t('deleteTransaction.italicDeleteInfo')}</i>
                    </Text>
                </li>
            </ol>
            <br />
            <Text color="secondary">
                {t('deleteTransaction.dateRangeInfo', {
                    startDate: moment(startDate).format('DD MMMM YYYY HH:mm'),
                    endDate: moment(endDate).format('DD MMMM YYYY HH:mm'),
                })}
            </Text>
            <Text color="secondary">{t('deleteTransaction.permanentInfo')}</Text>
            <br />
            <Separator />
            <br />
            <Heading heading="sectionTitle">{t('deleteTransaction.processTitle')}</Heading>
            <br />
            <ol style={{ marginTop: 0, paddingLeft: '1.5rem' }}>
                <li>
                    <Text color="secondary">{t('deleteTransaction.process1')}</Text>
                </li>
                <li>
                    <Text color="secondary">{t('deleteTransaction.process2')}</Text>
                </li>
                <li>
                    <Text color="secondary">{t('deleteTransaction.process3')}</Text>
                </li>
                <li>
                    <Text color="secondary">{t('deleteTransaction.process4')}</Text>
                </li>
                <li>
                    <Text color="secondary">{t('deleteTransaction.process5')}</Text>
                </li>
            </ol>
            <br />
            <Separator />
            <br />
            <Heading heading="sectionTitle">{t('deleteTransaction.requirements')}</Heading>
            <br />
            <Box css={{ padding: '$spacing-05', backgroundColor: '$bgGray', borderRadius: '$md' }}>
                <ol style={{ marginTop: 0, paddingLeft: '1.5rem', listStyleType: 'decimal' }}>
                    <li>
                        <Text color="primary">
                            {t('deleteTransaction.term1')} <b>{t('deleteTransaction.boldTerm1')}</b>
                        </Text>
                    </li>
                    <li>
                        <Text color="primary">
                            {t('deleteTransaction.term2')} <b>{t('deleteTransaction.boldTerm2')}</b>{' '}
                            {t('deleteTransaction.subTerm2')}
                        </Text>
                    </li>
                    <li>
                        <Text color="primary">{t('deleteTransaction.term3')}</Text>
                        <ol style={{ listStyleType: 'upper-alpha' }}>
                            <li>
                                <Text color="primary">{t('deleteTransaction.term3a')}</Text>
                            </li>
                            <li>
                                <Text color="primary">{t('deleteTransaction.term3b')}</Text>
                            </li>
                            <li>
                                <Text color="primary">{t('deleteTransaction.term3c')}</Text>
                                <ol style={{ listStyleType: 'lower-roman' }}>
                                    {[
                                        'Semua Laporan Penjualan',
                                        'Semua Laporan Dapur',
                                        'Semua Laporan Produk',
                                        'Semua Laporan Kasir',
                                        'Semua Laporan Poin & Loyalti',
                                        'Semua Laporan Pajak',
                                        'Semua Analisa Laporan',
                                        'Semua Laporan Keuangan',
                                        'Semua Laporan Persediaan',
                                    ].map((report, i) => (
                                        <li key={i}>
                                            <Text color="primary">{report}</Text>
                                        </li>
                                    ))}
                                </ol>
                            </li>
                        </ol>
                    </li>
                    <li>
                        <Text color="primary">{t('deleteTransaction.term4')}</Text>
                    </li>
                    <li>
                        <Text color="primary">{t('deleteTransaction.term5')}</Text>
                    </li>
                </ol>
            </Box>
            <br />
            <Flex direction="column" gap={3}>
                <Text color="secondary" css={{ fontWeight: 600 }}>
                    {t('deleteTransaction.agreement')}
                </Text>
                <Controller
                    name="agree1"
                    control={control}
                    rules={{ required: true }}
                    render={({ field: { onChange, value } }) => (
                        <InputCheckbox
                            checked={value}
                            onCheckedChange={onChange}
                            label={<CheckboxLabel>{t('deleteTransaction.cb1')}</CheckboxLabel>}
                            isInvalid={!!errors.agree1}
                        />
                    )}
                />
                <Controller
                    name="agree2"
                    control={control}
                    rules={{ required: true }}
                    render={({ field: { onChange, value } }) => (
                        <InputCheckbox
                            checked={value}
                            onCheckedChange={onChange}
                            label={<CheckboxLabel>{t('deleteTransaction.cb2')}</CheckboxLabel>}
                            isInvalid={!!errors.agree2}
                        />
                    )}
                />
                <Controller
                    name="agree3"
                    control={control}
                    rules={{ required: true }}
                    render={({ field: { onChange, value } }) => (
                        <InputCheckbox
                            checked={value}
                            onCheckedChange={onChange}
                            label={<CheckboxLabel>{t('deleteTransaction.cb3')}</CheckboxLabel>}
                            isInvalid={!!errors.agree3}
                        />
                    )}
                />
            </Flex>
        </Paper>
    );
};

export default StepTwo;
