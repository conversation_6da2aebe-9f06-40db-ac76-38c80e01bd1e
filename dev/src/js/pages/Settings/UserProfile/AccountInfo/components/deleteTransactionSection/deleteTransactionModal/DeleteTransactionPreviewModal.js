import React from 'react';
import PropTypes from 'prop-types';
import { PageDialog, Paper, Heading, Text, Flex, Grid, Box, InputCheckbox, Button } from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';

const Row = ({ label, value }) => (
    <Grid css={{ gridTemplateColumns: '1fr 2fr', gap: 0, mb: '$spacing-02' }}>
        <Box
            css={{
                backgroundColor: '$gray100',
                borderTopLeftRadius: '$md',
                borderBottomLeftRadius: '$md',
                px: '$spacing-05',
                py: '$spacing-04',
            }}
        >
            <Text color="primary" css={{ fontWeight: 600 }}>
                {label}
            </Text>
        </Box>
        <Box
            css={{
                backgroundColor: '$gray100',
                borderTopRightRadius: '$md',
                borderBottomRightRadius: '$md',
                px: '$spacing-05',
                py: '$spacing-04',
            }}
        >
            <Text color="primary">{value}</Text>
        </Box>
    </Grid>
);

const DeleteTransactionPreviewModal = ({ isOpen, onOpenChange, data, onConfirm }) => {
    const { t, isMobile } = useAccountInfo();

    const formatDT = dt => (dt ? moment(dt).format('DD MMMM YYYY, HH:mm') : '-');

    return (
        <PageDialog open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
            <PageDialog.Title>{t('deleteTransaction.preview.title', 'Penghapusan Data')}</PageDialog.Title>
            <PageDialog.Content wrapperSize="lg">
                <Paper responsive css={{ display: 'flex', flexDirection: 'column', gap: '$spacing-06' }}>
                    <Heading as="h4" heading="sectionTitle">
                        {t('deleteTransaction.preview.detailTitle', 'Detail Data')}
                    </Heading>

                    <Flex
                        align="start"
                        gap={2}
                        css={{
                            backgroundColor: '$gray100',
                            px: '$spacing-05',
                            py: '$spacing-04',
                            borderRadius: '$lg',
                        }}
                    >
                        <CircleInfoFilled />
                        <Text color="secondary" css={{ flex: 1 }}>
                            {t(
                                'deleteTransaction.preview.cancellationInfo',
                                'Anda masih dapat melakukan pembatalan hingga maksimal 5 menit sebelum waktu eksekusi',
                            )}
                        </Text>
                    </Flex>

                    <Box>
                        <Row label={t('deleteTransaction.preview.outletName', 'Nama Outlet')} value={data?.outletName || '-'} />
                        <Row label={t('deleteTransaction.preview.ownerEmail', 'Email Owner')} value={data?.email || '-'} />
                        <Row
                            label={t('deleteTransaction.preview.dateRange', 'Rentang Tanggal')}
                            value={`${formatDT(moment(data?.startDate).startOf('day'))} - ${formatDT(data?.endDate)}`}
                        />
                        <Row
                            label={t('deleteTransaction.preview.approvedAt', 'Tanggal & Jam Persetujuan')}
                            value={formatDT(data?.approvalAt)}
                        />
                        <Row
                            label={t('deleteTransaction.preview.executedAt', 'Tanggal & Jam Eksekusi')}
                            value={`${moment(data?.executionAt).format('DD MMMM YYYY')}, 00:00`}
                        />
                    </Box>

                    <Box>
                        <Text css={{ fontWeight: 600 }} color="secondary">
                            {t('deleteTransaction.preview.confirmationLabel', 'Konfirmasi:')}
                        </Text>
                        <Flex direction="column" gap={3} css={{ mt: '$spacing-03' }}>
                            <InputCheckbox
                                disabled
                                checked={!!data?.agree1}
                                label={
                                    <Text color="secondary">
                                        {t(
                                            'deleteTransaction.cb1',
                                            'Saya memahami bahwa semua transaksi dan data terkait akan dihapus secara permanen',
                                        )}
                                    </Text>
                                }
                            />
                            <InputCheckbox
                                disabled
                                checked={!!data?.agree2}
                                label={
                                    <Text color="secondary">
                                        {t('deleteTransaction.cb2', 'Saya telah mencadangkan data yang diperlukan')}
                                    </Text>
                                }
                            />
                            <InputCheckbox
                                disabled
                                checked={!!data?.agree3}
                                label={
                                    <Text color="secondary">
                                        {t('deleteTransaction.cb3', 'Saya setuju untuk melanjutkan proses penghapusan')}
                                    </Text>
                                }
                            />
                        </Flex>
                    </Box>
                </Paper>
            </PageDialog.Content>
            <PageDialog.Footer css={{ display: 'flex', justifyContent: 'center' }}>
                <Flex css={{ width: '$full', '@lg': { width: 980, marginLeft: '320px' } }} justify="end" gap={3}>
                    <Button
                        buttonType="ghost"
                        onClick={() => onOpenChange(false)}
                        css={{ width: isMobile ? '$full' : 'auto' }}
                    >
                        {t('deleteTransaction.preview.back', 'Kembali')}
                    </Button>
                    <Button
                        buttonType="negative"
                        onClick={() => {
                            if (onConfirm) onConfirm();
                        }}
                        css={{ width: isMobile ? '$full' : 'auto' }}
                    >
                        {t('deleteTransaction.preview.delete', 'Hapus Transaksi')}
                    </Button>
                </Flex>
            </PageDialog.Footer>
        </PageDialog>
    );
};

DeleteTransactionPreviewModal.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
    data: PropTypes.shape({
        outletName: PropTypes.string,
        email: PropTypes.string,
        startDate: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        endDate: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        approvalAt: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        executionAt: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
        agree1: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
        agree2: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
        agree3: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
    }),
    onConfirm: PropTypes.func,
};

DeleteTransactionPreviewModal.defaultProps = {
    isOpen: false,
    onOpenChange: () => {},
    data: {},
    onConfirm: () => {},
};

export default DeleteTransactionPreviewModal;
