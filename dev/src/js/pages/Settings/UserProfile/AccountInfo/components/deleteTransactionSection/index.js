import React, { useState } from 'react';
import { Flex, Heading, Button, Text } from '@majoo-ui/react';
import { useAccountInfo } from '../../AccountInfoContext';
import AccountVerificationModal from './AccountVerificationModal';
import DeleteTransactionModal from './deleteTransactionModal/DeleteTransactionModal';

const DeleteTransactionSection = () => {
    const { t, isMobile } = useAccountInfo();
    const [verifModalOpen, setVerifModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);

    return (
        <React.Fragment>
            <Flex direction={!isMobile ? 'row' : 'column'} justify="between" gap={3}>
                <Flex direction="column" gap={3}>
                    <Heading as="h3" heading="sectionTitle" css={{ '@sm': { mb: '$spacing-05 !important' } }}>
                        {t('deleteTransaction.title', '<PERSON><PERSON>ju<PERSON>')}
                    </Heading>
                    <Text css={{ color: '$textDisable', width: '$full', '@lg': { width: 250 } }}>
                        {t(
                            'deleteTransaction.info',
                            'Proses hapus akun akan memutus semua integrasi pada platform majoo',
                        )}
                    </Text>
                </Flex>
                <Flex direction={!isMobile ? 'row' : 'column'} css={{ width: !isMobile ? '70%' : '100%' }} gap={4}>
                    <Button
                        type="button"
                        buttonType="negative-secondary"
                        onClick={() => {
                            setVerifModalOpen(true);
                        }}
                        size="sm"
                    >
                        {t('deleteTransaction.button')}
                    </Button>
                </Flex>
            </Flex>
            {verifModalOpen && (
                <AccountVerificationModal
                    isOpen={verifModalOpen}
                    onOpenChange={setVerifModalOpen}
                    onVerifSuccess={() => {
                        setVerifModalOpen(false);
                        setDeleteModalOpen(true);
                    }}
                />
            )}
            {deleteModalOpen && (
                <DeleteTransactionModal isOpen={deleteModalOpen} onOpenChange={setDeleteModalOpen} />
            )}
        </React.Fragment>
    );
};

export default DeleteTransactionSection;
