import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    DialogClose,
    Button,
    Text,
    Box,
    InputText,
    Flex,
    Paragraph,
    FormGroup,
    FormHelper,
} from '@majoo-ui/react';
import { styled } from '@stitches/react';
import { getNestedProperty } from '~/utils/helper';

const ResendContainer = styled(Flex, {
    userSelect: 'none',
    variants: {
        disabled: {
            true: {
                cursor: 'not-allowed',
                color: '$textSecondary !important',
            },
            false: {
                cursor: 'pointer',
                color: '$textSecondary !important',
            },
        },
    },
});

const ResendButton = styled(Paragraph, {
    fontSize: '14px',
    fontWeight: 'bold',
});

const VerificationModal = ({
    isOpen,
    onOpenChange,
    verificationType,
    requestVerificationCode,
    onSubmit,
    isMobile,
    isAuth,
    t,
    contactValue,
    maxLength,
}) => {
    const [isDisabled, setDisabled] = useState(true);
    const [timerResend, setTimerResend] = useState(0);
    const [verificationCode, setVerificationCode] = useState('');
    const [resendCount, setResendCount] = useState(0);
    const [errorMessage, setErrorMessage] = useState();

    const timer = useRef({});

    const startResendTimer = () => {
        setTimerResend(30);
        timer.current = setInterval(() => {
            setTimerResend(prev => {
                if (prev <= 1) {
                    clearInterval(timer.current);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const resendOtp = () => {
        if (resendCount < 3) {
            setErrorMessage('');
            setVerificationCode('');
            requestVerificationCode(
                verificationType,
                contactValue,
                () => {
                    setResendCount(prev => prev + 1);
                    startResendTimer();
                },
                e => {
                    if (getNestedProperty(e, 'message', '').toLowerCase().includes('daily limit exceeded')) {
                        setResendCount(3);
                    }
                },
            );
        }
    };

    const formatTimer = seconds => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const handleSubmit = () => {
        onSubmit(verificationCode, verificationType, e => {
            const errMsg = getNestedProperty(e, 'message', '').toLowerCase();
            if (errMsg.includes('invalid code')) {
                setErrorMessage(t('verificationModal.errors.invalidCode', 'Kode tidak sesuai'));
            }
            if (errMsg.includes('no active otp found')) {
                setErrorMessage(t('verificationModal.errors.expiredCode', 'Kode tidak berlaku. Kirim ulang untuk mendapatkan kode baru.'));
            }
            if (errMsg.includes('blocked')) {
                onOpenChange(false);
            }
        });
    };

    let modalTitle;
    if (isAuth) {
        modalTitle = verificationType === 'email'
            ? t('verificationModal.title.emailAuth', 'Verifikasi Via Email')
            : t('verificationModal.title.phoneAuth', 'Verifikasi Via Whatsapp');
    } else {
        modalTitle = verificationType === 'email'
            ? t('verificationModal.title.email', 'Verifikasi Email')
            : t('verificationModal.title.phone', 'Verifikasi Telepon');
    }

    useEffect(() => {
        if (isOpen && timerResend === 0) {
            startResendTimer();
            setResendCount(1);
        }

        if (isOpen) {
            setResendCount(1);
        }

        return () => {
            if (timer.current) {
                clearInterval(timer.current);
            }
        };
    }, [isOpen]);

    return (
        <ModalDialog modal open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile}>
            <ModalDialogTitle>{modalTitle}</ModalDialogTitle>
            <ModalDialogContent>
                <Box css={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {/* Description */}
                    <Box css={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                        <Text
                            css={{
                                fontSize: '14px',
                                lineHeight: '1.71',
                                color: '$textPrimary',
                                fontWeight: 400,
                            }}
                        >
                            {t('verificationModal.description', `Masukkan ${maxLength} digit kode verifikasi yang dikirimkan ke`)}
                        </Text>
                        <Text
                            css={{
                                fontSize: '14px',
                                lineHeight: '1.43',
                                color: '$textPrimary',
                                fontWeight: 600,
                            }}
                        >
                            {contactValue}
                        </Text>
                    </Box>

                    {/* Input Field */}
                    <Box>
                        <FormGroup>
                            <InputText
                                placeholder={t('verificationModal.placeholder', 'Masukkan kode')}
                                maxLength={maxLength}
                                isInvalid={!!errorMessage}
                                value={verificationCode}
                                onChange={e => {
                                    setVerificationCode(e.target.value);
                                    if (e.target.value !== '') {
                                        setDisabled(false);
                                    } else {
                                        setDisabled(true);
                                    }
                                }}
                            />
                            {errorMessage && <FormHelper error>{errorMessage}</FormHelper>}
                        </FormGroup>
                    </Box>
                    <ResendContainer gap={2} disabled={timerResend > 0 || resendCount >= 3} align="center">
                        <ResendButton
                            onClick={() => (timerResend === 0 && resendCount < 3 ? resendOtp() : {})}
                            color={timerResend === 0 && resendCount < 3 ? 'green' : 'secondary'}
                        >
                            {resendCount < 3
                                ? t('verificationModal.resendButton', 'Kirim Ulang')
                                : t('verificationModal.resendLimit', 'Kirim ulang kode bisa dilakukan setelah 24 jam.')
                            }
                        </ResendButton>
                        {resendCount < 3 && (
                            <Paragraph css={{ fontSize: '14px', fontWeight: 400, color: '$textTertiary' }}>
                                {timerResend > 0 ? formatTimer(timerResend) : ''}
                            </Paragraph>
                        )}
                    </ResendContainer>
                </Box>
            </ModalDialogContent>
            <ModalDialogFooter>
                <Flex gap="4" justify="end" css={{ width: '100%' }}>
                    <DialogClose asChild>
                        <Button buttonType="ghost" size="md" css={{ padding: '10px 12px' }}>
                            {t('verificationModal.cancelButton', 'Batal')}
                        </Button>
                    </DialogClose>
                    <Button disabled={isDisabled} size="md" onClick={handleSubmit}>
                        {t('verificationModal.verifyButton', 'Verifikasi')}
                    </Button>
                </Flex>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

VerificationModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    verificationType: PropTypes.string,
    requestVerificationCode: PropTypes.func.isRequired,
    onSubmit: PropTypes.func,
    isMobile: PropTypes.bool,
    isAuth: PropTypes.bool,
    t: PropTypes.func.isRequired,
    contactValue: PropTypes.string,
    maxLength: PropTypes.number,
};

VerificationModal.defaultProps = {
    verificationType: 'phone1',
    onSubmit: () => {},
    isMobile: false,
    isAuth: false,
    contactValue: '',
    maxLength: 4,
};

export default VerificationModal;
