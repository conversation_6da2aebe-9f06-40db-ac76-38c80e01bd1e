import React from 'react';
import PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    ModalDialogFooter,
    DialogClose,
    Button,
    Flex,
    Paragraph,
} from '@majoo-ui/react';
import { useMediaQuery } from '../../../../../utils/useMediaQuery';

const VerificationFailedModal = ({ isOpen, onOpenChange, t }) => {
    const isMobile = useMediaQuery('(max-width: 767px)');
    return (
        <ModalDialog modal open={isOpen} onOpenChange={onOpenChange} isMobile={isMobile} hideCloseButton>
            <ModalDialogTitle>Verifikasi OTP Gagal</ModalDialogTitle>
            <ModalDialogContent>
                <Paragraph align="center" color="primary">
                    Anda telah gagal memasukkan OTP sebanyak 5 kali. Proses hapus data dibatalkan. <PERSON>lakan ajukan
                    kembali jika masih ingin melanjutkan.
                </Paragraph>
            </ModalDialogContent>
            <ModalDialogFooter>
                <Flex gap="4" justify="end" css={{ width: '100%' }}>
                    <DialogClose asChild>
                        <Button buttonType="ghost" size="md" css={{ padding: '10px 12px' }}>
                            Tutup
                        </Button>
                    </DialogClose>
                </Flex>
            </ModalDialogFooter>
        </ModalDialog>
    );
};

VerificationFailedModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    t: PropTypes.func.isRequired,
};

export default VerificationFailedModal;
