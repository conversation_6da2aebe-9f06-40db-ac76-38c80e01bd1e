import React, { useState, useContext } from 'react';
import PropTypes from 'prop-types';
import { PageDialog, Flex, StepWizard, Button, AlertDialog, ToastContext } from '@majoo-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import moment from 'moment';
import { useAccountInfo } from '../../../AccountInfoContext';
import StepOne from './StepOne';
import StepTwo from './StepTwo';
import VerificationModal from '../../../components/verificationModal';
import * as employeeApi from '~/data/employee';
import { catchError, getNestedProperty } from '~/utils/helper';
import VerificationFailedModal from '../../verificationFailedModal';
import DeleteTransactionPreviewModal from './DeleteTransactionPreviewModal';
import { createDeleteTransaction } from '~/data/users';

const navContents = t => [{ title: t('deleteTransaction.step1Title') }, { title: t('deleteTransaction.step2Title') }];

const DeleteTransactionModal = ({ isOpen, onOpenChange }) => {
    const { t, isMobile, formData, showProgress, hideProgress, listOutlet } = useAccountInfo();
    const { addToast } = useContext(ToastContext);

    const [sw, setSw] = useState({});
    const [completedSteps, setCompletedSteps] = useState([false, false]);
    const [isOpenAlert, setIsOpenAlert] = useState(false);

    // Authorization and Verification Modal states
    const [verificationModalOpen, setVerificationModalOpen] = useState(false);
    const [verifFailModalOpen, setVerifFailModalOpen] = useState(false);
    const [previewOpen, setPreviewOpen] = useState(true);
    const [previewData, setPreviewData] = useState(null);

    const form = useForm({
        mode: 'onChange',
        defaultValues: {
            step: 1,
            outletId: '',
            startDate: moment().startOf('month').toDate(),
            endDate: moment().toDate(),
            agree1: 0,
            agree2: 0,
            agree3: 0,
        },
        resolver: values => {
            const errors = {};
            const { startDate, endDate, outletId } = values;

            if (!outletId) {
                errors.outletId = {
                    type: 'required',
                    message: t('validation:form.required'),
                };
            }

            if (moment(endDate).isBefore(moment(startDate))) {
                errors.endDate = {
                    type: 'manual',
                    message: t('deleteTransaction.validation.endDate'),
                };
            }

            if (moment(endDate).isBefore(moment().subtract(4, 'days').startOf('day'))) {
                errors.endDate = {
                    type: 'manual',
                    message: t('deleteTransaction.validation.endDate'),
                };
            }

            if (moment(startDate).diff(moment(endDate), 'days', true) > 30) {
                errors.startDate = {
                    type: 'manual',
                    message: t('deleteTransaction.validation.dateRange'),
                };
            }

            return {
                values,
                errors,
            };
        },
    });

    const {
        watch,
        setValue,
        handleSubmit,
        formState: { isValid },
    } = form;
    const step = watch('step');
    const agree1 = watch('agree1');
    const agree2 = watch('agree2');
    const agree3 = watch('agree3');

    // Request verification code for authorization
    const fetchRequestVerifCode = async (onSuccess, onError) => {
        showProgress();
        const verificationPayload = {
            type: 'authorize_purge_transaction',
            value: formData.email,
            send_to: 'email',
        };

        try {
            await employeeApi.requestVerificationCode(verificationPayload);
            if (onSuccess) onSuccess();
            setVerificationModalOpen(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
            const errMsg = getNestedProperty(error, 'message', '').toLowerCase();
            if (errMsg.includes('blocked')) {
                setVerifFailModalOpen(true);
            }
        } finally {
            hideProgress();
        }
    };

    // Verify the code entered by user
    const fetchVerifyData = async (code, onError) => {
        showProgress();
        try {
            await employeeApi.verifyCode({
                type: 'authorize_purge_transaction',
                code,
            });

            // Call createDeleteTransaction after successful verification
            const formDataValues = form.getValues();
            const payload = {
                outlet_id: Number(formDataValues.outletId),
                start_date: moment(formDataValues.startDate).format('YYYY-MM-DD'),
                end_date: moment(formDataValues.endDate).format('YYYY-MM-DD'),
                code,
            };
            
            const res = await createDeleteTransaction(payload);

            // Prepare preview data regardless of API response shape
            const selectedOutlet = (listOutlet || []).find(o => String(o.id_cabang) === String(formDataValues.outletId));
            const now = moment();
            const execution = moment().add(1, 'day').startOf('day');
            setPreviewData({
                outletName: selectedOutlet ? selectedOutlet.cabang_name : String(formDataValues.outletId),
                email: formData.email,
                startDate: formDataValues.startDate,
                endDate: formDataValues.endDate,
                approvalAt: now.toDate(),
                executionAt: execution.toDate(),
                agree1,
                agree2,
                agree3,
                response: res,
            });

            setVerificationModalOpen(false);
            setPreviewOpen(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const go = dir => {
        if (dir === 'back') {
            setValue('step', step - 1);
            sw.previousStep?.();
            return;
        }
        // next
        setCompletedSteps(prev => prev.map((_, idx) => idx < step));
        setValue('step', step + 1);
        sw.nextStep?.();
    };

    const onSave = () => {
        fetchRequestVerifCode();
    };

    const FooterButtons = () => (
        <Flex justify="end" gap={3} css={{ flex: 1 }}>
            <Button
                type="button"
                buttonType="ghost"
                css={{ width: isMobile ? '$full' : 'auto' }}
                onClick={() => {
                    if (step === 1) setIsOpenAlert(true);
                    else go('back');
                }}
            >
                {t(step === 1 ? 'translation:label.cancel' : 'translation:label.back')}
            </Button>
            <Button
                type="button"
                css={{ width: isMobile ? '$full' : 'auto' }}
                disabled={(step === 1 && !isValid) || (step === 2 && !(agree1 && agree2 && agree3))}
                onClick={handleSubmit(data => (step === 1 ? go('next') : onSave(data)))}
            >
                {t(step === 1 ? 'translation:label.nextThen' : 'translation:label.save')}
            </Button>
        </Flex>
    );

    return (
        <React.Fragment>
            <PageDialog open={isOpen} onOpenChange={() => setIsOpenAlert(true)} isMobile={isMobile}>
                <PageDialog.Title>{t('deleteTransaction.title')}</PageDialog.Title>
                <PageDialog.Content wrapperSize="lg">
                    <FormProvider {...form}>
                        <form id="form-delete-transaction">
                            <StepWizard
                                completedSteps={completedSteps}
                                initialStep={1}
                                navContents={navContents(t)}
                                setSw={setSw}
                                forbidClickNav
                                isMobile={isMobile}
                            >
                                <StepOne />
                                <StepTwo />
                            </StepWizard>
                        </form>
                    </FormProvider>

                    <AlertDialog
                        dialogType="negative"
                        isMobile={isMobile}
                        open={isOpenAlert}
                        title={t('translation:label.cancelled', 'Batal')}
                        description={t('deleteTransaction.alertCancel')}
                        labelConfirm={t('translation:label.continue')}
                        labelCancel={t('translation:label.cancel')}
                        onCancel={() => setIsOpenAlert(false)}
                        onConfirm={() => onOpenChange(false)}
                    />
                </PageDialog.Content>

                <PageDialog.Footer css={{ display: 'flex', justifyContent: 'center' }}>
                    <Flex
                        css={{
                            width: '$full',
                            maxWidth: 980,
                            margin: '0 auto',
                        }}
                    >
                        <FooterButtons />
                    </Flex>
                </PageDialog.Footer>
            </PageDialog>
            {/* Verification Modal */}
            {verificationModalOpen && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModalOpen}
                    onOpenChange={setVerificationModalOpen}
                    verificationType="email"
                    contactValue={formData.email}
                    requestVerificationCode={async (_type, _contactValue, onSuccess, onError) => {
                        await fetchRequestVerifCode(onSuccess, onError);
                    }}
                    onSubmit={(code, _, onError) => fetchVerifyData(code, onError)}
                    t={t}
                    maxLength={6}
                />
            )}
            {verifFailModalOpen && (
                <VerificationFailedModal
                    isOpen={verifFailModalOpen}
                    onOpenChange={open => {
                        if (!open) {
                            setVerifFailModalOpen(false);
                            onOpenChange(false);
                        }
                    }}
                    t={t}
                />
            )}
            {previewOpen && (
                <DeleteTransactionPreviewModal
                    isOpen={previewOpen}
                    onOpenChange={setPreviewOpen}
                    data={previewData}
                    onConfirm={() => {
                        // Close preview and parent modal upon confirm
                        setPreviewOpen(false);
                        onOpenChange(false);
                    }}
                />
            )}
        </React.Fragment>
    );
};

DeleteTransactionModal.propTypes = {
    isOpen: PropTypes.bool,
    onOpenChange: PropTypes.func,
};

DeleteTransactionModal.defaultProps = {
    isOpen: false,
    onOpenChange: () => {},
};

export default DeleteTransactionModal;
