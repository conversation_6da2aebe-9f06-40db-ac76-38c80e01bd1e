import { fetchApi } from '../../services/api';

const endPoints = {
    cekPin: 'user/cek_pin',
    checkFile: 'user/check_file',
    getWizzardData: 'user/wizzardOne',
    getUserByReferal: 'user/user_referal',
    getCouponList: 'user_referal_voucher/user_referal_list',
    infoAkun: 'user/info_akun',
    ownerData: 'user/owner_data',
    ownerAppData: 'api/v1/owner-apps/account',
    login: '0_0_11/user/login',
    resendActivationCode: 'user/sendcode',
    activateUser: 'user/activatedCode',
    createInitialItem: '0_0_12/user/useritem',
    postRegisterSurvey: 'user/usersurvey',
    journeyChange: '0_0_10/user/userJourney',
    changeNumber: '0_0_10/user/change_number',
    sendVerificationCode: '0_0_10/user/send_verify_number',
    verifyNumber: '0_0_10/user/verify_number',
    getDataKota: '0_0_12/wilayah/kota',
    newUpdateUsahaUser: '0_0_12/user/userusaha',
    userBussinessInfo: 'user/infousaha',
    userReset: 'user/reset',
    userProfile: '0_0_12/user_profile',
    userProfileStatus: '/0_0_12/user_profile/status',
    userRekeningSendOTP: '0_0_12/user_profile/send_otp',
    userRekeningValidateOTP: '0_0_12/user_profile/validate_otp',
    userRekeningPayrollStatus: 'user_profile/rekening_detail',
    userFavoriteMenu: 'api/v1/user/favorite-menu',
    setSettlement: '0_0_12/user_profile/settlement',
    requestVerificationCode: '0_0_9/user/send_verification',
    validateVerificationCode: 'verification/registration_number',
    userRegisterV9: '0_0_9/user/register',
    ssoUserRegister: '0_0_9/user/register_sso',
    getDataKecamatan: 'wilayah/kecamatan',
    userProfiling: 'user/profiling',
    resendActivationCodeV2: '0_0_2/user/sendCode',
    onboardingTask: 'user/onboarding-task',
    onboardingCustomData: 'user/coach_mark',
    deleteAccountDetail: 'DeleteAccount/detail',
    deleteAccount: 'DeleteAccount',
    deleteAccountCheck: 'DeleteAccount/check',
    putTncMal: 'user/tnc_mal',
    deleteTransaction: 'purge_transaction/submission',
    blockDeleteTransaction: 'purge_transaction/block',
    verifyDeleteTransactionPassword: 'purge_transaction/verify_delete',
};

export const getUserByReferal = payload => fetchApi(endPoints.getUserByReferal, payload);
export const getWizzardData = payload => fetchApi(endPoints.getWizzardData, payload);
export const cekPin = payload => fetchApi(endPoints.cekPin, payload, 'post');
export const checkFile = payload => fetchApi(endPoints.checkFile, payload, 'post'); // download file
export const getCouponList = payload => fetchApi(endPoints.getCouponList, payload);
export const updateInfoAkun = (payload, version = '') =>
    fetchApi(`${version ? `${version}/` : ''}${endPoints.infoAkun}`, payload, 'put');
export const login = (payload, captchaToken) =>
    fetchApi(endPoints.login, payload, 'post', {
        customHeader: { 'x-security-key': captchaToken },
    });
export const resendActivationCode = payload => fetchApi(endPoints.resendActivationCode, payload, 'post');
export const activateUser = payload => fetchApi(endPoints.activateUser, payload, 'post');
export const createInitialItem = payload => fetchApi(endPoints.createInitialItem, payload, 'post');
export const postRegisterSurvey = payload => fetchApi(endPoints.postRegisterSurvey, payload, 'post');
export const journeyChange = payload => fetchApi(endPoints.journeyChange, payload, 'put');
export const changeNumber = payload => fetchApi(endPoints.changeNumber, payload, 'put');
export const sendVerificationCode = payload => fetchApi(endPoints.sendVerificationCode, payload, 'post');
export const verifyNumber = payload => fetchApi(endPoints.verifyNumber, payload, 'post');
export const getDataKota = payload => fetchApi(endPoints.getDataKota, payload, 'get');
export const newUpdateUsahaUser = payload => fetchApi(endPoints.newUpdateUsahaUser, payload, 'put');
export const userResetPassword = (payload, captchaToken) =>
    fetchApi(endPoints.userReset, payload, 'post', {
        customHeader: { 'x-security-key': captchaToken },
    });
export const getUserBussinessInfo = payload => fetchApi(endPoints.userBussinessInfo, payload);

export const getAccountInfo = payload => fetchApi(endPoints.infoAkun, payload);
export const getOwnerData = payload => fetchApi(endPoints.ownerData, payload);
export const getOwnerAppData = payload =>
    fetchApi(endPoints.ownerAppData, payload, 'get', {
        serviceDomainType: 'user-management',
        authType: 'bearer',
    });
export const getListSubmissionProfile = payload => fetchApi(endPoints.userProfile, payload);
export const getDetailSubmissionProfile = id => fetchApi(endPoints.userProfile, {}, 'get', { slashId: id });
export const createSubmissionProfile = payload => fetchApi(endPoints.userProfile, payload, 'post');
export const updateSubmissionProfile = payload => fetchApi(endPoints.userProfile, payload, 'put');
export const patchSubmissionProfile = payload => fetchApi(endPoints.userProfile, payload, 'patch');
export const changeStatusActiveRekening = payload => fetchApi(endPoints.userProfileStatus, payload, 'put');
export const deleteSubmissionProfile = id => fetchApi(endPoints.userProfile, {}, 'delete', { slashId: id });
export const userRekeningSendOTP = payload => fetchApi(endPoints.userRekeningSendOTP, payload, 'post');
export const userRekeningValidateOTP = payload => fetchApi(endPoints.userRekeningValidateOTP, payload, 'post');
export const userRekeningPayrollStatus = payload => fetchApi(endPoints.userRekeningPayrollStatus, payload, 'put');

// Menu Favorite
export const getUserFavoriteMenu = payload =>
    fetchApi(endPoints.userFavoriteMenu, payload, 'get', {
        serviceDomainType: 'user-management',
        authType: 'bearer',
    });
export const createFavoriteMenu = id =>
    fetchApi(endPoints.userFavoriteMenu, {}, 'post', {
        slashId: id,
        serviceDomainType: 'user-management',
        authType: 'bearer',
    });
export const deleteFavoriteMenu = id =>
    fetchApi(endPoints.userFavoriteMenu, {}, 'put', {
        slashId: id,
        serviceDomainType: 'user-management',
        authType: 'bearer',
    });

// update settlement
export const setSettlement = payload => fetchApi(endPoints.setSettlement, payload, 'put');
// New Register Flow
export const requestVerificationCode = (payload, captchaToken) =>
    fetchApi(endPoints.requestVerificationCode, payload, 'post', {
        customHeader: { 'x-security-key': captchaToken },
    });
export const validateVerificationCode = payload => fetchApi(endPoints.validateVerificationCode, payload, 'put');
export const userRegisterV9 = payload => fetchApi(endPoints.userRegisterV9, payload, 'post');
export const ssoUserRegister = payload => fetchApi(endPoints.ssoUserRegister, payload, 'post');
export const getDataKecamatan = (payload, query) =>
    fetchApi(endPoints.getDataKecamatan, payload, 'get', { queryString: query });
export const getUserProfiling = () => fetchApi(endPoints.userProfiling, {}, 'get');
export const setUserProfiling = payload => fetchApi(endPoints.userProfiling, payload, 'post');
export const resendActivationCodeV2 = payload => fetchApi(endPoints.resendActivationCodeV2, payload, 'post');

export const getOnboardingTask = () => fetchApi(endPoints.onboardingTask);
export const updateOnboardingTask = (id, payload) =>
    fetchApi(endPoints.onboardingTask, payload, 'put', {
        slashId: id,
    });

export const getOnboardingCustomData = () => fetchApi(endPoints.onboardingCustomData);
export const updateOnboardingCustomData = payload => fetchApi(endPoints.onboardingCustomData, payload, 'put');
export const getDeleteAccountDetail = () => fetchApi(endPoints.deleteAccountDetail);
export const postDeleteAccount = () => fetchApi(endPoints.deleteAccount, {}, 'post');
export const checkDeleteAccount = () => fetchApi(endPoints.deleteAccountCheck);
// tnc mal
export const putTncMal = () => fetchApi(endPoints.putTncMal, {}, 'put');

// Purge Transaction APIs
export const createDeleteTransaction = payload => fetchApi(endPoints.deleteTransaction, payload, 'post');

export const getDeleteTransactionStatus = payload => fetchApi(endPoints.deleteTransaction, payload, 'get');

export const cancelDeleteTransaction = id => fetchApi(endPoints.deleteTransaction, {}, 'delete', { slashId: id });

export const blockDeleteTransaction = payload => fetchApi(endPoints.blockDeleteTransaction, payload, 'post');

export const verifyDeleteTransactionAccount = payload => fetchApi(endPoints.verifyDeleteTransactionPassword, payload, 'post');
