import * as api from './api';

export const getCouponList = payload => api.getCouponList(payload);
export const cekPin = payload => api.cekPin(payload);

// download file
export const checkFile = payload => api.checkFile(payload);
export const getWizzardData = payload => api.getWizzardData(payload);
export const getUserByReferal = payload => api.getUserByReferal(payload);
export const updateInfoAkun = (payload, version = '') => api.updateInfoAkun(payload, version);
export const login = (payload, captchaToken) => api.login(payload, captchaToken);
export const resendActivationCode = payload => api.resendActivationCode(payload);
export const activateUser = payload => api.activateUser(payload);
export const createInitialItem = payload => api.createInitialItem(payload);
export const registrationSurvey = payload => api.postRegisterSurvey(payload);
export const journeyChange = payload => api.journeyChange(payload);
export const changeNumber = payload => api.changeNumber(payload);
export const sendVerificationCode = payload => api.sendVerificationCode(payload);
export const verifyNumber = payload => api.verifyNumber(payload);
export const getUserBussinessInfo = payload => api.getUserBussinessInfo(payload);
export const userResetPassword = (payload, captchaToken) => api.userResetPassword(payload, captchaToken);
export const getAccountInfo = payload => api.getAccountInfo(payload);
export const getOwnerData = payload => api.getOwnerData(payload);
export const getOwnerAppData = payload => api.getOwnerAppData(payload);

export const getDataKota = payload => api.getDataKota(payload);

export const newUpdateUsahaUser = payload => api.newUpdateUsahaUser(payload);
export const getListSubmissionProfile = payload => api.getListSubmissionProfile(payload);
export const getDetailSubmissionProfile = id => api.getDetailSubmissionProfile(id);
export const createSubmissionProfile = payload => api.createSubmissionProfile(payload);
export const updateSubmissionProfile = payload => api.updateSubmissionProfile(payload);
export const patchSubmissionProfile = payload => api.patchSubmissionProfile(payload);
export const changeStatusActiveRekening = payload => api.changeStatusActiveRekening(payload);
export const deleteSubmissionProfile = id => api.deleteSubmissionProfile(id);
export const userRekeningSendOTP = payload => api.userRekeningSendOTP(payload);
export const userRekeningValidateOTP = payload => api.userRekeningValidateOTP(payload);
export const userRekeningPayrollStatus = payload => api.userRekeningPayrollStatus(payload);

// Menu Favorite
export const getUserFavoriteMenu = payload => api.getUserFavoriteMenu(payload);
export const createFavoriteMenu = id => api.createFavoriteMenu(id);
export const deleteFavoriteMenu = id => api.deleteFavoriteMenu(id);

// update settlement
export const setSettlement = payload => api.setSettlement(payload);
// New Register Flow
export const requestVerificationCode = (payload, captchaToken) => api.requestVerificationCode(payload, captchaToken);
export const validateVerificationCode = payload => api.validateVerificationCode(payload);
export const userRegisterV9 = payload => api.userRegisterV9(payload);
export const ssoUserRegister = payload => api.ssoUserRegister(payload);
export const getDataKecamatan = (payload, query) => api.getDataKecamatan(payload, query);
export const getUserProfiling = () => api.getUserProfiling();
export const setUserProfiling = payload => api.setUserProfiling(payload);
export const resendActivationCodeV2 = payload => api.resendActivationCodeV2(payload);

export const getOnboardingTask = () => api.getOnboardingTask();
export const updateOnboardingTask = (id, payload) => api.updateOnboardingTask(id, payload);

export const getOnboardingCustomData = () => api.getOnboardingCustomData();
export const updateOnboardingCustomData = (id, payload) => api.updateOnboardingCustomData(id, payload);
export const getDeleteAccountDetail = () => api.getDeleteAccountDetail();
export const postDeleteAccount = () => api.postDeleteAccount();
export const checkDeleteAccount = () => api.checkDeleteAccount();

// tnc mal
export const putTncMal = () => api.putTncMal();

// Purge Transaction APIs
export const createDeleteTransaction = payload => api.createDeleteTransaction(payload);
export const getDeleteTransactionStatus = payload => api.getDeleteTransactionStatus(payload);
export const cancelDeleteTransaction = id => api.cancelDeleteTransaction(id);
export const blockDeleteTransaction = payload => api.blockDeleteTransaction(payload);
export const verifyDeleteTransactionAccount = payload => api.verifyDeleteTransactionAccount(payload);
