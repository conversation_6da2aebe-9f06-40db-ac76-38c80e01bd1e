{"title": "Account Information", "optional": "Optional", "verif": "Verification", "file": "Select or drop files here", "profil": {"title": "Profile Account Info", "name": "Name", "email": "Email", "phone1": "1st Phone No", "phone2": "2nd Phone No", "phone3": "3rd Phone No"}, "personal": {"title": "Personal Info", "ktp": "ID Card Number", "npwp": "TIN Number", "upload_ktp": "Upload ID Card", "desc1": "Currently being verified.", "desc2": "The maximum verification process is 7 working days.", "upload_npwp": "Upload TIN"}, "permission": {"title": "Access Rights Info", "access": "Access Rights"}, "address": {"title": "Address Info", "location": "Address", "country": "Country", "province": "Province", "city": "City", "subdistrict": "Subdistrict"}, "creds": {"title": "Change Password", "old": "Old Password", "new": "New Password", "confirm_new": "Confirm New Password"}, "appsOwner": {"title": "Owner App", "upgradeSubscribe": "majoo Owner is an application to make it easier for you to monitor your business in real time. Please upgrade to Advance and Prime packages to use this feature.", "phoneNo": "Phone No", "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending"}, "banner": {"title": "Download the Application Now!", "desc": "Immediately download and install the owner application for convenience in monitoring your business in real time."}}, "verif1": {"title": "Account Verification", "desc": "The verification code has been sent via WhatsApp to", "code": "Verification Code", "desc1": "Didn't get a code?", "resend": "Resend", "desc2": "Please wait {{sec}} seconds to resend"}, "authorization": {"title": "Account Profile Authorization", "description": "Choose one of the methods below to get the verification code", "whatsapp": "WhatsApp to {{phone}}", "email": "Email to {{email}}"}, "verificationModal": {"title": {"email": "Email Verification", "phone": "Phone Verification", "emailAuth": "Verification Via Email", "phoneAuth": "Verification Via WhatsApp"}, "description": "Enter the 4-digit verification code sent to", "placeholder": "Enter code", "resendButton": "Resend", "resendLimit": "Code resend can be done after 24 hours.", "cancelButton": "Cancel", "verifyButton": "Verify", "errors": {"invalidCode": "Code does not match", "expiredCode": "Code is invalid. Resend to get a new code."}}, "v": {"name": "*Please fill in the Name", "format_email": "*Incorrect email format", "email_empty": "*Email cannot be empty", "phone1": "*Please fill in the 1st Phone No", "ktp": "*Please fill in the ID Card Number", "ktp1": "*Please upload photo ID", "ktp_verif": "*ID Card has not been verified, please upload your ID Card so we can verify it", "npwp_verif": "*TIN has not been verified, please upload your ID Card so we can verify it", "address": "*Please fill in the Address", "province": "*Please select a Province", "city": "*Please select a City", "subdistrict": "*Please select a Subdistrict", "pin": "*PIN is required", "too_large": "file size too large", "pin_digit": "*PIN must be in 6 digits", "new_password": "*Password must be at least 8 characters, contain numbers, letters and symbols", "new_password1": "*Password does not match", "infoacc": "Account information failed to save", "oldpw": "Old password is incorrect", "verif_inc": "Incorrect verification code, please check again.", "id_r": "*ID verification is rejected", "npwp_r": "*TIN verification is rejected", "emailUnverified": "Email has not been verified", "phone1Unverified": "The 1st phone has not been verified", "phone2Unverified": "The 2nd phone has not been verified", "phone3Unverified": "The 3rd phone has not been verified"}, "save": "Save Account Information", "desc": "The information that has been inputted will be stored and used as the main data. Continue?", "success": {"email": "Email successfully verified", "phone": "Phone number successfully verified", "infoacc": "Account information successfully saved"}, "language": {"indonesia": "Indonesia", "english": "English", "title": "Select Language", "label": "Language"}, "delete": {"title": "Request Delete Account", "info": "The account deletion process will cut off all integration on Majoo platform", "button": "Delete Account", "toastSuccess": "Account deletion successfully requested", "viewInformation": "View Information", "footer": "Account deletion requests will be processed within <strong>{{days}} days.</strong>", "footer2": "Make sure all data and information has been backed up first", "createdAt": "This account applied for deletion on: {{ date }} (max. 3 working days)", "viewInformationService": "View deleted service information", "modalDelete": {"title": "Request Delete Account", "desc": "To continue, majo<PERSON> needs to confirm account ownership", "password": "Enter Password"}, "alert": {"title": "Request Account Deletion", "title2": "Information Delete Account", "cancelTitle": "<PERSON><PERSON> Delete Account", "description": "<PERSON><PERSON> will process your account deletion maximum <strong>3 working days.</strong> Make sure you back up the important data. Continue?", "description2": "Your account will be deleted within 1 hour, and all accounts connected to the device will be forced to exit.", "cancelDescription": "Canceling an account deletion will not affect your Majoo account data and information. Continue?"}, "information": {"title": "Deleted Services Information", "confirmDeletion": "Confirm Deletion", "confirmDeletionInfo": "Your Majoo account will be deleted based on your consent. When an account is deleted, all data and access to Majoo services will be removed without the possibility of recovery. Make sure to backup your data before continue.", "removedFeature": "Services that will be removed:", "removedFeature2": "Deleted Services Information Services that will be removed:", "note": "<strong>Note:</strong> Make sure all important data, such as outlet information, reports, employee data, etc. is backed up", "tnc": "Yes, I understand the consequences of deleting an account and all the data in it", "outlet": {"title": "Outlet and Subscription", "desc": "Total outlets: {{total}} Outlets", "removedOutlet": "List of Outlets that will be deleted", "outletDesc": "{{paket}} Package, ends {{date}}"}, "webstore": {"title": "Online Store", "desc": "Outlet balance: {{total}}", "button": "Balance Details"}, "qris": {"title": "QRIS", "desc": "Remaining balance: {{total}}", "content": "The balance will be processed into the account 1 day after the account successfully deleted"}, "integrasi": {"title": "Service Integration"}, "other": {"title": "Other Services", "desc": "All transaction data and reports will be deleted"}}, "deleteTransaction": {"title": "Request Delete Transaction", "info": "The transaction deletion process will delete all transaction data on the selected outlet", "button": "Delete Transaction", "step1Title": "Delete Transaction Confirmation", "step2Title": "Deletion Confirmation", "alertCancel": "Are you sure you want to cancel the transaction deletion request?", "confirmTitle": "Delete Transaction Confirmation", "featureInfo": "This feature allows you to delete all transactions on the account, including:", "feature1": "Sales data, including refund and void transactions", "feature2": "Accounting transaction data related to sales (all sales reports, all kitchen reports, all product reports, all Poin & Loyalty Reports, all reports, all tax reports, all report analysis and all financial reports)", "outletDeleteInfo": "When the outlet data is deleted, all data on the selected outlet will be deleted without the possibility of being restored.", "selectOutlet": "Select Outlet", "dateRangeNote": "Dates can only be selected up to 3 days back, and the date range is no more than 1 month", "startDate": "Select Start Date", "endDate": "Select End Date", "caution1": "Before proceeding with the transaction deletion request, make sure all devices connected to this outlet are synchronized with the system.", "caution2": "All devices must synchronize at least on the same day you submit the deletion.", "caution3": "This is to ensure that no new transactions are still stored on the device (offline mode) and have not been sent to the system.", "termsTitle": "Deletion Confirmation", "deleteInfo": "Deleting transactions allows you to delete all transactions on your account, including some of the data mentioned in the previous step.", "deleteInfo1": "Sales data including refunds & voids", "deleteInfo2": "Accounting transaction data on sales transactions from all POS, Invoice, Online Store,", "deleteInfo3": "Inventory data on sales transactions from all POS, Invoice, Online Store,", "italicDeleteInfo": "Marketplace, Food Order and Consumer App.", "dateRangeInfo": "In the date and time range : {{startDate}} - {{endDate}}", "permanentInfo": "Deletion is permanent and cannot be recovered.", "processTitle": "Process & Implementation Time", "process1": "After you approve this agreement, the system will schedule data deletion", "process2": "Deletion will be carried out after you have successfully carried out the verification stage", "process3": "Transaction data deletion will be carried out 1 day after submission, right on D+1 at 00:00 WIB", "process4": "During the waiting period, the data is still available on the system and can be cancelled. Cancellation can still be done up to 5 minutes before execution time.", "process5": "After deletion is complete, all data mentioned will be permanently lost from the system", "requirements": "Terms and Conditions:", "term1": "Deletion is", "boldTerm1": "complete and cannot be undone", "term2": "You are responsible for", "boldTerm2": "back up or export", "subTerm2": "the required data before deletion is carried out.", "term3": "The deleted data includes:", "term3a": "All sales transactions including refund and void transactions", "term3b": "All sales transactions recorded in the Stock List menu include refunds and voids", "term3c": "Accounting data connected to these transactions includes:", "term4": "It should be noted that there may be differences in transaction data between majoo and transactions originating from the Online Store, Consumer Application, Marketplace, and Food Order. These differences can occur because deleting transactions affects sales records.", "term5": "You cannot make any claims or responsibilities as a result of deleting this data to majoo.", "agreement": "By checking the box below, you declare that you have read, understood and agree to all the Terms & Conditions above", "cb1": "I understand that all related transactions and data will be permanently deleted", "cb2": "I have backed up the necessary data", "cb3": "I agree to proceed with the deletion process", "validation": {"startDate": "Date cannot be more than 3 days back", "endDate": "End date cannot be less than start date", "dateRange": "Date range cannot be more than 1 month"}, "preview": {"title": "Data Deletion", "detailTitle": "Data Details", "cancellationInfo": "You can still cancel up to a maximum of 5 minutes before the execution time", "outletName": "Outlet Name", "ownerEmail": "Owner <PERSON><PERSON>", "dateRange": "Date Range", "approvedAt": "Approval Date & Time", "executedAt": "Execution Date & Time", "confirmationLabel": "Confirmation:", "back": "Back", "delete": "Delete Transaction"}}}}